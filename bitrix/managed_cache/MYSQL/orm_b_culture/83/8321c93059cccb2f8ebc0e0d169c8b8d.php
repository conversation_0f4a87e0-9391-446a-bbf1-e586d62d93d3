<?
if($INCLUDE_FROM_CACHE!='Y')return false;
$datecreate = '001748821982';
$dateexpire = '001748908382';
$ser_content = 'a:2:{s:7:"CONTENT";s:0:"";s:4:"VARS";a:1:{i:0;a:25:{s:2:"ID";s:1:"1";s:4:"CODE";s:2:"en";s:4:"NAME";s:2:"en";s:11:"FORMAT_DATE";s:10:"MM/DD/YYYY";s:15:"FORMAT_DATETIME";s:20:"MM/DD/YYYY H:MI:SS T";s:11:"FORMAT_NAME";s:18:"#NAME# #LAST_NAME#";s:10:"WEEK_START";s:1:"1";s:7:"CHARSET";s:5:"UTF-8";s:9:"DIRECTION";s:1:"Y";s:17:"SHORT_DATE_FORMAT";s:5:"n/j/Y";s:18:"MEDIUM_DATE_FORMAT";s:6:"M j, Y";s:16:"LONG_DATE_FORMAT";s:6:"F j, Y";s:16:"FULL_DATE_FORMAT";s:9:"l, F j, Y";s:16:"DAY_MONTH_FORMAT";s:3:"F j";s:22:"DAY_SHORT_MONTH_FORMAT";s:3:"M j";s:24:"DAY_OF_WEEK_MONTH_FORMAT";s:6:"l, F j";s:30:"SHORT_DAY_OF_WEEK_MONTH_FORMAT";s:6:"D, F j";s:36:"SHORT_DAY_OF_WEEK_SHORT_MONTH_FORMAT";s:6:"D, M j";s:17:"SHORT_TIME_FORMAT";s:5:"g:i a";s:16:"LONG_TIME_FORMAT";s:7:"g:i:s a";s:8:"AM_VALUE";s:2:"am";s:8:"PM_VALUE";s:2:"pm";s:26:"NUMBER_THOUSANDS_SEPARATOR";s:1:",";s:24:"NUMBER_DECIMAL_SEPARATOR";s:1:".";s:15:"NUMBER_DECIMALS";s:1:"2";}}}';
return true;
?>