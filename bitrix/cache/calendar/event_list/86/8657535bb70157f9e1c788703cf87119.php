<?
if($INCLUDE_FROM_CACHE!='Y')return false;
$datecreate = '001748862648';
$dateexpire = '001751454648';
$ser_content = 'a:2:{s:7:"CONTENT";s:0:"";s:4:"VARS";a:3:{s:15:"resultEntryList";a:1:{i:0;a:34:{s:2:"ID";s:2:"29";s:9:"PARENT_ID";s:2:"29";s:4:"NAME";s:21:"POD for Load# 8559797";s:8:"OWNER_ID";s:1:"1";s:5:"RRULE";s:0:"";s:6:"EXDATE";s:0:"";s:9:"DATE_FROM";s:22:"06/02/2025 12:00:00 pm";s:7:"DATE_TO";s:22:"06/02/2025 12:30:00 pm";s:7:"TZ_FROM";s:16:"America/New_York";s:5:"TZ_TO";s:16:"America/New_York";s:14:"TZ_OFFSET_FROM";s:6:"-14400";s:12:"TZ_OFFSET_TO";s:6:"-14400";s:10:"IS_MEETING";b:1;s:14:"MEETING_STATUS";s:1:"H";s:8:"CAL_TYPE";s:4:"user";s:9:"DT_LENGTH";i:1800;s:12:"DT_SKIP_TIME";s:1:"N";s:10:"SECTION_ID";s:1:"4";s:16:"DATE_FROM_TS_UTC";s:10:"1748894400";s:14:"DATE_TO_TS_UTC";s:10:"1748896200";s:7:"SECTION";O:36:"Bitrix\\Calendar\\Internals\\EO_Section":9:{s:10:"'.chr(0).'*'.chr(0).'_entity";O:22:"Bitrix\\Main\\ORM\\Entity":16:{s:12:"'.chr(0).'*'.chr(0).'className";s:39:"\\Bitrix\\Calendar\\Internals\\SectionTable";s:9:"'.chr(0).'*'.chr(0).'module";N;s:7:"'.chr(0).'*'.chr(0).'name";s:7:"Section";s:17:"'.chr(0).'*'.chr(0).'connectionName";s:7:"default";s:14:"'.chr(0).'*'.chr(0).'dbTableName";s:18:"b_calendar_section";s:10:"'.chr(0).'*'.chr(0).'primary";a:1:{i:0;s:2:"ID";}s:16:"'.chr(0).'*'.chr(0).'autoIncrement";s:2:"ID";s:8:"'.chr(0).'*'.chr(0).'uf_id";N;s:8:"'.chr(0).'*'.chr(0).'isUts";b:0;s:8:"'.chr(0).'*'.chr(0).'isUtm";b:0;s:9:"'.chr(0).'*'.chr(0).'fields";a:26:{s:2:"ID";O:35:"Bitrix\\Main\\ORM\\Fields\\IntegerField":27:{s:7:"'.chr(0).'*'.chr(0).'name";s:2:"ID";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:0:{}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";N;s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";O:22:"Bitrix\\Main\\ORM\\Entity":16:{s:12:"'.chr(0).'*'.chr(0).'className";s:39:"\\Bitrix\\Calendar\\Internals\\SectionTable";s:9:"'.chr(0).'*'.chr(0).'module";N;s:7:"'.chr(0).'*'.chr(0).'name";s:7:"Section";s:17:"'.chr(0).'*'.chr(0).'connectionName";s:7:"default";s:14:"'.chr(0).'*'.chr(0).'dbTableName";s:18:"b_calendar_section";s:10:"'.chr(0).'*'.chr(0).'primary";a:1:{i:0;s:2:"ID";}s:16:"'.chr(0).'*'.chr(0).'autoIncrement";s:2:"ID";s:8:"'.chr(0).'*'.chr(0).'uf_id";N;s:8:"'.chr(0).'*'.chr(0).'isUts";b:0;s:8:"'.chr(0).'*'.chr(0).'isUtm";b:0;s:9:"'.chr(0).'*'.chr(0).'fields";a:26:{s:2:"ID";r:40;s:4:"NAME";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:4:"NAME";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:12:"validateName";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:12:"validateName";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:4:"NAME";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:6:"XML_ID";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:6:"XML_ID";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:13:"validateXmlId";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:13:"validateXmlId";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:6:"XML_ID";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:11:"EXTERNAL_ID";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:11:"EXTERNAL_ID";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:18:"validateExternalId";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:18:"validateExternalId";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:11:"EXTERNAL_ID";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:16:"GAPI_CALENDAR_ID";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:16:"GAPI_CALENDAR_ID";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:22:"validateGapiCalendarId";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:22:"validateGapiCalendarId";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:16:"GAPI_CALENDAR_ID";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:6:"ACTIVE";O:35:"Bitrix\\Main\\ORM\\Fields\\BooleanField":27:{s:7:"'.chr(0).'*'.chr(0).'name";s:6:"ACTIVE";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:0:{}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";N;s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:1:{i:0;a:2:{i:0;r:206;i:1;s:14:"normalizeValue";}}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:6:"ACTIVE";s:16:"'.chr(0).'*'.chr(0).'default_value";s:1:"Y";s:9:"'.chr(0).'*'.chr(0).'values";a:2:{i:0;s:1:"N";i:1;s:1:"Y";}}s:11:"DESCRIPTION";O:32:"Bitrix\\Main\\ORM\\Fields\\TextField":29:{s:7:"'.chr(0).'*'.chr(0).'name";s:11:"DESCRIPTION";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:0:{}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";N;s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:11:"DESCRIPTION";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;s:7:"'.chr(0).'*'.chr(0).'long";b:0;}s:5:"COLOR";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:5:"COLOR";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:13:"validateColor";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:13:"validateColor";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:5:"COLOR";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:10:"TEXT_COLOR";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:10:"TEXT_COLOR";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:17:"validateTextColor";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:17:"validateTextColor";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:10:"TEXT_COLOR";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:6:"EXPORT";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:6:"EXPORT";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:14:"validateExport";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:14:"validateExport";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:6:"EXPORT";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:4:"SORT";O:35:"Bitrix\\Main\\ORM\\Fields\\IntegerField":27:{s:7:"'.chr(0).'*'.chr(0).'name";s:4:"SORT";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:0:{}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";N;s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:4:"SORT";s:16:"'.chr(0).'*'.chr(0).'default_value";i:100;s:7:"'.chr(0).'*'.chr(0).'size";i:4;}s:8:"CAL_TYPE";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:8:"CAL_TYPE";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:15:"validateCalType";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:15:"validateCalType";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:8:"CAL_TYPE";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:8:"OWNER_ID";O:35:"Bitrix\\Main\\ORM\\Fields\\IntegerField":27:{s:7:"'.chr(0).'*'.chr(0).'name";s:8:"OWNER_ID";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:0:{}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";N;s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:8:"OWNER_ID";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:7:"'.chr(0).'*'.chr(0).'size";i:4;}s:10:"CREATED_BY";O:35:"Bitrix\\Main\\ORM\\Fields\\IntegerField":27:{s:7:"'.chr(0).'*'.chr(0).'name";s:10:"CREATED_BY";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:0:{}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";N;s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:1;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:10:"CREATED_BY";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:7:"'.chr(0).'*'.chr(0).'size";i:4;}s:9:"PARENT_ID";O:35:"Bitrix\\Main\\ORM\\Fields\\IntegerField":27:{s:7:"'.chr(0).'*'.chr(0).'name";s:9:"PARENT_ID";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:0:{}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";N;s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:9:"PARENT_ID";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:7:"'.chr(0).'*'.chr(0).'size";i:4;}s:11:"DATE_CREATE";O:36:"Bitrix\\Main\\ORM\\Fields\\DatetimeField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:11:"DATE_CREATE";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:0:{}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";N;s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:11:"DATE_CREATE";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:14:"'.chr(0).'*'.chr(0).'useTimezone";b:1;}s:11:"TIMESTAMP_X";O:36:"Bitrix\\Main\\ORM\\Fields\\DatetimeField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:11:"TIMESTAMP_X";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:0:{}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";N;s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:11:"TIMESTAMP_X";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:14:"'.chr(0).'*'.chr(0).'useTimezone";b:1;}s:12:"DAV_EXCH_CAL";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:12:"DAV_EXCH_CAL";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:18:"validateDavExchCal";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:18:"validateDavExchCal";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:12:"DAV_EXCH_CAL";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:12:"DAV_EXCH_MOD";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:12:"DAV_EXCH_MOD";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:18:"validateDavExchMod";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:18:"validateDavExchMod";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:12:"DAV_EXCH_MOD";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:11:"CAL_DAV_CON";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:11:"CAL_DAV_CON";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:17:"validateCalDavCon";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:17:"validateCalDavCon";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:11:"CAL_DAV_CON";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:11:"CAL_DAV_CAL";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:11:"CAL_DAV_CAL";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:17:"validateCalDavCal";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:17:"validateCalDavCal";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:11:"CAL_DAV_CAL";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:11:"CAL_DAV_MOD";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:11:"CAL_DAV_MOD";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:17:"validateCalDavMod";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:17:"validateCalDavMod";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:11:"CAL_DAV_MOD";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:11:"IS_EXCHANGE";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:11:"IS_EXCHANGE";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:18:"validateIsExchange";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:18:"validateIsExchange";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:11:"IS_EXCHANGE";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:10:"SYNC_TOKEN";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:10:"SYNC_TOKEN";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:17:"validateSyncToken";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:17:"validateSyncToken";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:10:"SYNC_TOKEN";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:10:"PAGE_TOKEN";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:10:"PAGE_TOKEN";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:17:"validatePageToken";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:17:"validatePageToken";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:10:"PAGE_TOKEN";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}s:13:"EXTERNAL_TYPE";O:34:"Bitrix\\Main\\ORM\\Fields\\StringField":28:{s:7:"'.chr(0).'*'.chr(0).'name";s:13:"EXTERNAL_TYPE";s:11:"'.chr(0).'*'.chr(0).'dataType";N;s:20:"'.chr(0).'*'.chr(0).'initialParameters";a:1:{s:10:"validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:20:"validateExternalType";}}s:8:"'.chr(0).'*'.chr(0).'title";N;s:13:"'.chr(0).'*'.chr(0).'validation";a:2:{i:0;s:38:"Bitrix\\Calendar\\Internals\\SectionTable";i:1;s:20:"validateExternalType";}s:13:"'.chr(0).'*'.chr(0).'validators";N;s:23:"'.chr(0).'*'.chr(0).'additionalValidators";a:0:{}s:24:"'.chr(0).'*'.chr(0).'fetchDataModification";N;s:21:"'.chr(0).'*'.chr(0).'fetchDataModifiers";a:0:{}s:31:"'.chr(0).'*'.chr(0).'additionalFetchDataModifiers";a:0:{}s:23:"'.chr(0).'*'.chr(0).'saveDataModification";N;s:20:"'.chr(0).'*'.chr(0).'saveDataModifiers";N;s:30:"'.chr(0).'*'.chr(0).'additionalSaveDataModifiers";a:0:{}s:15:"'.chr(0).'*'.chr(0).'isSerialized";b:0;s:14:"'.chr(0).'*'.chr(0).'parentField";N;s:9:"'.chr(0).'*'.chr(0).'entity";r:56;s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:0;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:0;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:13:"EXTERNAL_TYPE";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:9:"'.chr(0).'*'.chr(0).'format";N;s:7:"'.chr(0).'*'.chr(0).'size";N;}}s:12:"'.chr(0).'*'.chr(0).'fieldsMap";a:26:{i:0;r:40;i:1;r:70;i:2;r:104;i:3;r:138;i:4;r:172;i:5;r:206;i:6;r:239;i:7;r:269;i:8;r:303;i:9;r:337;i:10;r:371;i:11;r:399;i:12;r:433;i:13;r:461;i:14;r:489;i:15;r:517;i:16;r:546;i:17;r:575;i:18;r:609;i:19;r:643;i:20;r:677;i:21;r:711;i:22;r:745;i:23;r:779;i:24;r:813;i:25;r:847;}s:11:"'.chr(0).'*'.chr(0).'u_fields";N;s:7:"'.chr(0).'*'.chr(0).'code";s:26:"CALENDAR_INTERNALS_SECTION";s:13:"'.chr(0).'*'.chr(0).'references";a:0:{}s:10:"'.chr(0).'*'.chr(0).'isClone";b:1;}s:13:"'.chr(0).'*'.chr(0).'connection";N;s:13:"'.chr(0).'*'.chr(0).'is_primary";b:1;s:12:"'.chr(0).'*'.chr(0).'is_unique";b:0;s:14:"'.chr(0).'*'.chr(0).'is_required";b:0;s:18:"'.chr(0).'*'.chr(0).'is_autocomplete";b:1;s:13:"'.chr(0).'*'.chr(0).'is_private";b:0;s:14:"'.chr(0).'*'.chr(0).'is_nullable";b:0;s:12:"'.chr(0).'*'.chr(0).'is_binary";b:0;s:14:"'.chr(0).'*'.chr(0).'column_name";s:2:"ID";s:16:"'.chr(0).'*'.chr(0).'default_value";N;s:7:"'.chr(0).'*'.chr(0).'size";i:4;}s:4:"NAME";r:70;s:6:"XML_ID";r:104;s:11:"EXTERNAL_ID";r:138;s:16:"GAPI_CALENDAR_ID";r:172;s:6:"ACTIVE";r:206;s:11:"DESCRIPTION";r:239;s:5:"COLOR";r:269;s:10:"TEXT_COLOR";r:303;s:6:"EXPORT";r:337;s:4:"SORT";r:371;s:8:"CAL_TYPE";r:399;s:8:"OWNER_ID";r:433;s:10:"CREATED_BY";r:461;s:9:"PARENT_ID";r:489;s:11:"DATE_CREATE";r:517;s:11:"TIMESTAMP_X";r:546;s:12:"DAV_EXCH_CAL";r:575;s:12:"DAV_EXCH_MOD";r:609;s:11:"CAL_DAV_CON";r:643;s:11:"CAL_DAV_CAL";r:677;s:11:"CAL_DAV_MOD";r:711;s:11:"IS_EXCHANGE";r:745;s:10:"SYNC_TOKEN";r:779;s:10:"PAGE_TOKEN";r:813;s:13:"EXTERNAL_TYPE";r:847;}s:12:"'.chr(0).'*'.chr(0).'fieldsMap";a:26:{i:0;r:40;i:1;r:70;i:2;r:104;i:3;r:138;i:4;r:172;i:5;r:206;i:6;r:239;i:7;r:269;i:8;r:303;i:9;r:337;i:10;r:371;i:11;r:399;i:12;r:433;i:13;r:461;i:14;r:489;i:15;r:517;i:16;r:546;i:17;r:575;i:18;r:609;i:19;r:643;i:20;r:677;i:21;r:711;i:22;r:745;i:23;r:779;i:24;r:813;i:25;r:847;}s:11:"'.chr(0).'*'.chr(0).'u_fields";N;s:7:"'.chr(0).'*'.chr(0).'code";N;s:13:"'.chr(0).'*'.chr(0).'references";a:0:{}s:10:"'.chr(0).'*'.chr(0).'isClone";b:0;}s:9:"'.chr(0).'*'.chr(0).'_state";i:1;s:16:"'.chr(0).'*'.chr(0).'_actualValues";a:1:{s:2:"ID";i:4;}s:17:"'.chr(0).'*'.chr(0).'_currentValues";a:0:{}s:17:"'.chr(0).'*'.chr(0).'_runtimeValues";a:0:{}s:14:"'.chr(0).'*'.chr(0).'_customData";N;s:25:"'.chr(0).'*'.chr(0).'_onPrimarySetListeners";a:0:{}s:15:"'.chr(0).'*'.chr(0).'_authContext";N;s:20:"'.chr(0).'*'.chr(0).'_savingInProgress";b:0;}s:18:"SECTION_DAV_XML_ID";N;s:19:"DATE_FROM_FORMATTED";s:24:"Mon Jun 02 2025 12:00:00";s:17:"DATE_TO_FORMATTED";s:24:"Mon Jun 02 2025 12:30:00";s:7:"SECT_ID";s:1:"4";s:7:"OPTIONS";N;s:13:"ATTENDEE_LIST";a:0:{}s:9:"COLLAB_ID";N;s:10:"IMPORTANCE";s:6:"normal";s:13:"PRIVATE_EVENT";s:0:"";s:11:"DESCRIPTION";s:0:"";s:12:"~DESCRIPTION";s:0:"";s:17:"~USER_OFFSET_FROM";i:0;s:15:"~USER_OFFSET_TO";i:0;}}s:9:"userIndex";a:0:{}s:14:"dateTimeFormat";s:20:"MM/DD/YYYY H:MI:SS T";}}';
return true;
?>