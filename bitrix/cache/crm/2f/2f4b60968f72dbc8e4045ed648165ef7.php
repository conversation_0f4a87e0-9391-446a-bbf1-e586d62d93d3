<?
if($INCLUDE_FROM_CACHE!='Y')return false;
$datecreate = '001749049858';
$dateexpire = '001749136258';
$ser_content = 'a:2:{s:7:"CONTENT";s:0:"";s:4:"VARS";a:1:{s:9:"fieldData";a:10:{s:20:"UF_CRM_1746731522190";a:4:{s:2:"ID";s:20:"UF_CRM_1746731522190";s:4:"NAME";s:6:"Origin";s:4:"TYPE";s:7:"address";s:8:"MULTIPLE";b:0;}s:20:"UF_CRM_1746731545348";a:4:{s:2:"ID";s:20:"UF_CRM_1746731545348";s:4:"NAME";s:11:"Destination";s:4:"TYPE";s:7:"address";s:8:"MULTIPLE";b:0;}s:17:"UF_CRM_1746737283";a:4:{s:2:"ID";s:17:"UF_CRM_1746737283";s:4:"NAME";s:16:"Dispatcher\'s Fee";s:4:"TYPE";s:6:"double";s:8:"MULTIPLE";b:0;}s:17:"UF_CRM_1746742785";a:4:{s:2:"ID";s:17:"UF_CRM_1746742785";s:4:"NAME";s:9:"New Field";s:4:"TYPE";s:7:"address";s:8:"MULTIPLE";b:0;}s:20:"UF_CRM_1746743020199";a:4:{s:2:"ID";s:20:"UF_CRM_1746743020199";s:4:"NAME";s:23:"Rate Confirmation / BOL";s:4:"TYPE";s:4:"file";s:8:"MULTIPLE";b:1;}s:17:"UF_CRM_1748095996";a:4:{s:2:"ID";s:17:"UF_CRM_1748095996";s:4:"NAME";s:38:"Pickup Time (Required for Appointment)";s:4:"TYPE";s:8:"datetime";s:8:"MULTIPLE";b:0;}s:17:"UF_CRM_1748096778";a:4:{s:2:"ID";s:17:"UF_CRM_1748096778";s:4:"NAME";s:38:"Pickup Window End (Only if time range)";s:4:"TYPE";s:8:"datetime";s:8:"MULTIPLE";b:0;}s:17:"UF_CRM_1748096987";a:4:{s:2:"ID";s:17:"UF_CRM_1748096987";s:4:"NAME";s:40:"Delivery Time (Required for Appointment)";s:4:"TYPE";s:8:"datetime";s:8:"MULTIPLE";b:0;}s:17:"UF_CRM_1748097008";a:4:{s:2:"ID";s:17:"UF_CRM_1748097008";s:4:"NAME";s:40:"Delivery Window End (Only if time range)";s:4:"TYPE";s:8:"datetime";s:8:"MULTIPLE";b:0;}s:20:"UF_CRM_1748173015677";a:5:{s:2:"ID";s:20:"UF_CRM_1748173015677";s:4:"NAME";s:12:"Service Type";s:4:"TYPE";s:11:"enumeration";s:8:"MULTIPLE";b:0;s:6:"VALUES";a:3:{i:159;s:8:"Standard";i:160;s:6:"Leased";i:161;s:6:"Mozaic";}}}}}';
return true;
?>