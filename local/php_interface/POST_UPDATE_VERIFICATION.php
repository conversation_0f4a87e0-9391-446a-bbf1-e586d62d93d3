<?php
/**
 * Post-update verification script for Bitrix24 customizations
 * Run this script after updating to verify all customizations are working
 */

require_once($_SERVER["DOCUMENT_ROOT"]."/bitrix/modules/main/include/prolog_before.php");

if (!$USER->IsAdmin()) {
    die("Access denied. Admin rights required.");
}

echo "<h1>Bitrix24 Customizations Verification</h1>";

// Check Bitrix version
echo "<h2>System Information</h2>";
if (file_exists($_SERVER["DOCUMENT_ROOT"]."/bitrix/modules/main/classes/general/version.php")) {
    include($_SERVER["DOCUMENT_ROOT"]."/bitrix/modules/main/classes/general/version.php");
    echo "<p><strong>Bitrix Version:</strong> " . (defined('SM_VERSION') ? SM_VERSION : 'Unknown') . "</p>";
    echo "<p><strong>Version Date:</strong> " . (defined('SM_VERSION_DATE') ? SM_VERSION_DATE : 'Unknown') . "</p>";
}

echo "<h2>Customization Status</h2>";

// Check local directory files
$localFiles = [
    'local/php_interface/init.php' => 'Custom initialization',
    'local/php_interface/include/tel_links.php' => 'VoIP tel: links integration',
    'local/php_interface/include/menu_filter.php' => 'Menu filtering system',
    'local/php_interface/include/role_based_menu.php' => 'Role-based menu system',
    'local/admin/menu.php' => 'Custom admin menu',
    'local/bitrix24_bot/handler.php' => 'Bot integration'
];

foreach ($localFiles as $file => $description) {
    $fullPath = $_SERVER["DOCUMENT_ROOT"] . "/" . $file;
    $status = file_exists($fullPath) ? "✅ OK" : "❌ MISSING";
    echo "<p><strong>$description:</strong> $status ($file)</p>";
}

// Check custom modules
echo "<h3>Custom Modules</h3>";
$customModules = [
    'local/modules/custom.license/' => 'Custom license module',
    'local/modules/landing/' => 'Landing page customizations'
];

foreach ($customModules as $module => $description) {
    $fullPath = $_SERVER["DOCUMENT_ROOT"] . "/" . $module;
    $status = is_dir($fullPath) ? "✅ OK" : "❌ MISSING";
    echo "<p><strong>$description:</strong> $status ($module)</p>";
}

// Check template modifications
echo "<h3>Template Status</h3>";
$headerPath = $_SERVER["DOCUMENT_ROOT"] . "/bitrix/templates/bitrix24/header.php";
if (file_exists($headerPath)) {
    $headerContent = file_get_contents($headerPath);
    $hasRoleSystem = (strpos($headerContent, 'Role 8') !== false || strpos($headerContent, 'Role 9') !== false);
    
    if ($hasRoleSystem) {
        echo "<p><strong>Template Header:</strong> ⚠️ Still contains role system (may need migration)</p>";
    } else {
        echo "<p><strong>Template Header:</strong> ✅ Clean (role system should be in local/php_interface/include/role_based_menu.php)</p>";
    }
} else {
    echo "<p><strong>Template Header:</strong> ❌ MISSING</p>";
}

// Test CRM module
echo "<h3>Module Status</h3>";
if (CModule::IncludeModule('crm')) {
    echo "<p><strong>CRM Module:</strong> ✅ Available</p>";
    
    // Test permissions system
    if (class_exists('CCrmPerms')) {
        echo "<p><strong>CRM Permissions:</strong> ✅ Available</p>";
    } else {
        echo "<p><strong>CRM Permissions:</strong> ❌ Class not found</p>";
    }
} else {
    echo "<p><strong>CRM Module:</strong> ❌ Not available</p>";
}

// Test role-based system
echo "<h3>Role-Based System Test</h3>";
if (CModule::IncludeModule('crm') && class_exists('CCrmPerms')) {
    $userPerms = new CCrmPerms($USER->GetID());
    $hasLeadAccess = !$userPerms->HavePerm('LEAD', BX_CRM_PERM_NONE);
    $hasCatalogAccess = !$userPerms->HavePerm('CATALOG', BX_CRM_PERM_NONE);
    
    echo "<p><strong>Current User Lead Access:</strong> " . ($hasLeadAccess ? "✅ Yes" : "❌ No") . "</p>";
    echo "<p><strong>Current User Catalog Access:</strong> " . ($hasCatalogAccess ? "✅ Yes" : "❌ No") . "</p>";
    
    $isRole8 = !$hasLeadAccess && !$hasCatalogAccess;
    $isRole9 = $hasLeadAccess && !$hasCatalogAccess;
    
    if ($isRole8) {
        echo "<p><strong>Current User Role:</strong> Role 8 (Limited access)</p>";
    } elseif ($isRole9) {
        echo "<p><strong>Current User Role:</strong> Role 9 (Lead access)</p>";
    } else {
        echo "<p><strong>Current User Role:</strong> Full access (Admin/Manager)</p>";
    }
} else {
    echo "<p><strong>Role System:</strong> ❌ Cannot test - CRM module or permissions not available</p>";
}

// JavaScript test
echo "<h3>JavaScript Test</h3>";
echo '<script>
document.addEventListener("DOMContentLoaded", function() {
    // Test if tel: links are working
    var phoneLinks = document.querySelectorAll(\'a[href^="tel:"]\');
    var telLinksCount = phoneLinks.length;
    
    // Test if callto: links still exist (should be converted)
    var calltoLinks = document.querySelectorAll(\'a[href^="callto:"]\');
    var calltoLinksCount = calltoLinks.length;
    
    var testResults = document.getElementById("js-test-results");
    if (testResults) {
        testResults.innerHTML = 
            "<p><strong>Tel: links found:</strong> " + telLinksCount + "</p>" +
            "<p><strong>Callto: links found:</strong> " + calltoLinksCount + " (should be 0)</p>";
    }
});
</script>';

echo '<div id="js-test-results"><p>JavaScript test running...</p></div>';

echo "<h2>Recommended Actions</h2>";
echo "<ol>";
echo "<li>Test role-based access with different user accounts</li>";
echo "<li>Verify VoIP functionality by clicking phone numbers</li>";
echo "<li>Check that kanban phone icons work correctly</li>";
echo "<li>Test bot integration if applicable</li>";
echo "<li>Verify custom admin menu appears</li>";
echo "<li>Check console for JavaScript errors</li>";
echo "</ol>";

echo "<h2>Troubleshooting</h2>";
echo "<p>If any customizations are not working:</p>";
echo "<ol>";
echo "<li>Check error logs in local/logs/</li>";
echo "<li>Verify file permissions on local/ directory</li>";
echo "<li>Clear Bitrix cache (Settings → Performance → Clear Cache)</li>";
echo "<li>Check browser console for JavaScript errors</li>";
echo "<li>Restore from backup if necessary</li>";
echo "</ol>";

require_once($_SERVER["DOCUMENT_ROOT"]."/bitrix/modules/main/include/epilog_after.php");
?>
