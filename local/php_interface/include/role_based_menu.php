<?php
/**
 * Role-based menu system for Bitrix24
 * This file contains the role-based access control logic
 * that was previously in the template header
 */

// Only run if CRM module is available
if (!CModule::IncludeModule('crm')) {
    return;
}

global $USER;

// Check user permissions
$userPerms = new CCrmPerms($USER->GetID());
$hasLeadAccess = !$userPerms->HavePerm('LEAD', BX_CRM_PERM_NONE);
$hasCatalogAccess = !$userPerms->HavePerm('CATALOG', BX_CRM_PERM_NONE);

// Determine user roles
$isRole8 = !$hasLeadAccess && !$hasCatalogAccess;
$isRole9 = $hasLeadAccess && !$hasCatalogAccess;

// Add event handler to inject menu restrictions
AddEventHandler('main', 'OnEndBufferContent', 'InjectRoleBasedMenuRestrictions');

function InjectRoleBasedMenuRestrictions(&$content) {
    global $USER;
    
    if (!CModule::IncludeModule('crm')) {
        return;
    }
    
    $userPerms = new CCrmPerms($USER->GetID());
    $hasLeadAccess = !$userPerms->HavePerm('LEAD', BX_CRM_PERM_NONE);
    $hasCatalogAccess = !$userPerms->HavePerm('CATALOG', BX_CRM_PERM_NONE);
    
    $isRole8 = !$hasLeadAccess && !$hasCatalogAccess;
    $isRole9 = $hasLeadAccess && !$hasCatalogAccess;
    
    if (!$isRole8 && !$isRole9) {
        return; // No restrictions for other users
    }
    
    $menuScript = generateMenuRestrictionScript($isRole8, $isRole9, $hasLeadAccess, $hasCatalogAccess);
    
    // Inject before closing body tag
    $content = str_replace('</body>', $menuScript . '</body>', $content);
}

function generateMenuRestrictionScript($isRole8, $isRole9, $hasLeadAccess, $hasCatalogAccess) {
    ob_start();
    ?>
    
    <!-- Role-based Menu Restrictions -->
    <?php if ($isRole8): ?>
    <style>
    /* Hide Help and Sitemap buttons for Role 8 */
    .menu-help-btn,
    .menu-sitemap-btn {
        display: none !important;
    }
    
    /* Hide specific menu items for Role 8 */
    [data-id="menu_crm_lead"],
    [data-id="menu_lead"],
    div[id*="menu_crm_lead"],
    div[id*="menu_lead"],
    a[href*="/crm/lead/"],
    
    /* Analytics restrictions - hide all except Sales Funnel */
    [data-item-id="ANALYTICS_MANAGERS"],
    [data-item-id="ANALYTICS_CALLS"],
    [data-item-id="ANALYTICS_DIALOGS"],
    [data-item-id="CRM_TRACKING"],
    [data-item-id="REPORT"],
    [data-item-id="ANALYTICS_BI"],
    [data-item-id="BIC_DASHBOARDS"],
    [data-item-id="SALES_INTELLIGENCE"],
    a[href*="/report/analytics/"]:not([href*="analyticBoardKey=crm_sales_funnel"]),
    a[href*="/report/"]:not([href*="analyticBoardKey=crm_sales_funnel"]):not([href*="/report/analytics/?analyticBoardKey=crm_sales_funnel"]),
    a[href*="/crm/ml/"],
    a[href*="/ai/"],
    a[href*="crm-scoring"],
    a[href*="/crm/tracking/"] {
        display: none !important;
    }
    </style>
    
    <script>
    (function() {
        function hideRole8MenuItems() {
            try {
                // Hide Sales Intelligence specifically
                var salesIntelligenceLinks = document.querySelectorAll('a[href*="/crm/tracking/"]');
                for (var i = 0; i < salesIntelligenceLinks.length; i++) {
                    var link = salesIntelligenceLinks[i];
                    var parent = link;
                    for (var j = 0; j < 5; j++) {
                        if (parent && parent.parentNode) {
                            parent = parent.parentNode;
                            if (parent.classList &&
                                (parent.classList.contains('main-buttons-item') ||
                                 parent.classList.contains('crm-control-panel-item'))) {
                                parent.style.display = 'none';
                                break;
                            }
                        }
                    }
                }
            } catch (e) {
                console.log('Role 8 menu hiding error:', e);
            }
        }
        
        hideRole8MenuItems();
        setTimeout(hideRole8MenuItems, 1000);
        
        if (window.MutationObserver) {
            var observer = new MutationObserver(hideRole8MenuItems);
            observer.observe(document.documentElement || document.body, {
                childList: true,
                subtree: true
            });
        }
    })();
    </script>
    <?php endif; ?>
    
    <?php if ($isRole9): ?>
    <style>
    /* Hide Help and Sitemap buttons for Role 9 */
    .menu-help-btn,
    .menu-sitemap-btn {
        display: none !important;
    }
    
    /* Hide specific menu items for Role 9 */
    [data-id="menu_crm_store"],
    [data-id="menu_store"],
    [data-id="menu_catalog"],
    [data-id="menu_crm_product"],
    [data-id="menu_product"],
    [data-id="menu_contact_center"],
    [data-id="menu_openlines"],
    [data-id="menu_devops"],
    [data-id="menu_marketplace_dev"],
    [data-id="menu_rest_api"],
    [data-id="menu_automation"],
    [data-id="menu_bizproc"],
    [data-id="menu_rpa"],
    [data-id="menu_marketplace_group"],
    [data-id="menu_marketplace"],
    [data-id="menu_market"],
    [data-id="menu_configs"],
    [data-id="menu_settings"],
    [data-id="menu_bitrix24_settings"],
    [data-id="menu_bic_dashboards"] {
        display: none !important;
    }
    </style>
    
    <script>
    (function() {
        function hideRole9MenuItems() {
            try {
                var restrictedDataIds = [
                    'menu_crm_store', 'menu_store', 'menu_catalog', 'menu_crm_product', 'menu_product',
                    'menu_contact_center', 'menu_openlines', 'menu_devops', 'menu_marketplace_dev',
                    'menu_rest_api', 'menu_automation', 'menu_bizproc', 'menu_rpa',
                    'menu_marketplace_group', 'menu_marketplace', 'menu_market',
                    'menu_configs', 'menu_settings', 'menu_bitrix24_settings', 'menu_bic_dashboards'
                ];
                
                for (var i = 0; i < restrictedDataIds.length; i++) {
                    var elements = document.querySelectorAll('[data-id="' + restrictedDataIds[i] + '"]');
                    for (var j = 0; j < elements.length; j++) {
                        if (elements[j] && elements[j].style) {
                            elements[j].style.display = 'none';
                        }
                    }
                }
            } catch (e) {
                console.log('Role 9 menu hiding error:', e);
            }
        }
        
        hideRole9MenuItems();
        setTimeout(hideRole9MenuItems, 500);
    })();
    </script>
    <?php endif; ?>
    
    <?php
    return ob_get_clean();
}

// Handle server-side redirections
AddEventHandler('main', 'OnBeforeProlog', 'HandleRoleBasedRedirections');

function HandleRoleBasedRedirections() {
    global $APPLICATION, $USER;
    
    if (!CModule::IncludeModule('crm')) {
        return;
    }
    
    $userPerms = new CCrmPerms($USER->GetID());
    $hasLeadAccess = !$userPerms->HavePerm('LEAD', BX_CRM_PERM_NONE);
    $hasCatalogAccess = !$userPerms->HavePerm('CATALOG', BX_CRM_PERM_NONE);
    
    $isRole9 = $hasLeadAccess && !$hasCatalogAccess;
    $currentUrl = $APPLICATION->GetCurPage();
    
    // Handle Lead access restrictions
    if (!$hasLeadAccess) {
        $isLeadsPage = $currentUrl === '/crm/' || strpos($currentUrl, '/crm/lead/') === 0;
        if ($isLeadsPage) {
            LocalRedirect('/crm/deal/', false, "301 Moved Permanently");
            exit;
        }
    }
    
    // Handle Role 9 redirections
    if ($isRole9) {
        $isRole9RestrictedPage = 
            strpos($currentUrl, '/crm/catalog/') === 0 ||
            strpos($currentUrl, '/crm/product/') === 0 ||
            strpos($currentUrl, '/crm/store/') === 0 ||
            strpos($currentUrl, '/services/contact_center/') === 0 ||
            strpos($currentUrl, '/openlines/') === 0 ||
            strpos($currentUrl, '/devops/') === 0 ||
            strpos($currentUrl, '/rest/') === 0 ||
            strpos($currentUrl, '/marketplace/dev/') === 0 ||
            strpos($currentUrl, '/bizproc/') === 0 ||
            strpos($currentUrl, '/automation/') === 0 ||
            strpos($currentUrl, '/rpa/') === 0 ||
            strpos($currentUrl, '/marketplace/') === 0 ||
            strpos($currentUrl, '/market/') === 0 ||
            strpos($currentUrl, '/settings/configs/') === 0 ||
            strpos($currentUrl, '/settings/admin/') === 0 ||
            strpos($currentUrl, '/configs/') === 0;
            
        if ($isRole9RestrictedPage) {
            LocalRedirect('/crm/lead/', false, "301 Moved Permanently");
            exit;
        }
        
        if ($currentUrl === '/crm/') {
            LocalRedirect('/crm/lead/', false, "301 Moved Permanently");
            exit;
        }
    }
}
?>
