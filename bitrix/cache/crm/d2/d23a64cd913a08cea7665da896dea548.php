<?
if($INCLUDE_FROM_CACHE!='Y')return false;
$datecreate = '001749056812';
$dateexpire = '001749143212';
$ser_content = 'a:2:{s:7:"CONTENT";s:0:"";s:4:"VARS";a:1:{s:9:"fieldData";a:9:{s:22:"UF_CRM_4_1748121125139";a:4:{s:2:"ID";s:22:"UF_CRM_4_1748121125139";s:4:"NAME";s:10:"Net Amount";s:4:"TYPE";s:6:"string";s:8:"MULTIPLE";b:0;}s:19:"UF_CRM_4_1748177511";a:4:{s:2:"ID";s:19:"UF_CRM_4_1748177511";s:4:"NAME";s:12:"Service Type";s:4:"TYPE";s:6:"string";s:8:"MULTIPLE";b:0;}s:19:"UF_CRM_4_1748278870";a:4:{s:2:"ID";s:19:"UF_CRM_4_1748278870";s:4:"NAME";s:10:"Dispatcher";s:4:"TYPE";s:6:"string";s:8:"MULTIPLE";b:0;}s:22:"UF_CRM_4_1748452101811";a:4:{s:2:"ID";s:22:"UF_CRM_4_1748452101811";s:4:"NAME";s:7:"Carrier";s:4:"TYPE";s:6:"string";s:8:"MULTIPLE";b:0;}s:22:"UF_CRM_4_1748553788307";a:4:{s:2:"ID";s:22:"UF_CRM_4_1748553788307";s:4:"NAME";s:6:"Broker";s:4:"TYPE";s:6:"string";s:8:"MULTIPLE";b:0;}s:22:"UF_CRM_4_1748553812783";a:4:{s:2:"ID";s:22:"UF_CRM_4_1748553812783";s:4:"NAME";s:16:"Dispatcher\'s fee";s:4:"TYPE";s:6:"string";s:8:"MULTIPLE";b:0;}s:22:"UF_CRM_4_1748554135965";a:4:{s:2:"ID";s:22:"UF_CRM_4_1748554135965";s:4:"NAME";s:17:"Rate Confirmation";s:4:"TYPE";s:4:"file";s:8:"MULTIPLE";b:0;}s:22:"UF_CRM_4_1748639461941";a:4:{s:2:"ID";s:22:"UF_CRM_4_1748639461941";s:4:"NAME";s:3:"POD";s:4:"TYPE";s:4:"file";s:8:"MULTIPLE";b:0;}s:22:"UF_CRM_4_1748639789889";a:4:{s:2:"ID";s:22:"UF_CRM_4_1748639789889";s:4:"NAME";s:12:"Gross Amount";s:4:"TYPE";s:6:"string";s:8:"MULTIPLE";b:0;}}}}';
return true;
?>