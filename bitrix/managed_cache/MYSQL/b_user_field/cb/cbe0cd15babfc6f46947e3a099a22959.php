<?
if($INCLUDE_FROM_CACHE!='Y')return false;
$datecreate = '001748787418';
$dateexpire = '001748791018';
$ser_content = 'a:2:{s:7:"CONTENT";s:0:"";s:4:"VARS";a:2:{i:0;a:18:{s:2:"ID";s:1:"9";s:9:"ENTITY_ID";s:14:"CALENDAR_EVENT";s:10:"FIELD_NAME";s:16:"UF_CRM_CAL_EVENT";s:12:"USER_TYPE_ID";s:3:"crm";s:6:"XML_ID";N;s:4:"SORT";s:3:"100";s:8:"MULTIPLE";s:1:"Y";s:9:"MANDATORY";s:1:"N";s:11:"SHOW_FILTER";s:1:"N";s:12:"SHOW_IN_LIST";s:1:"Y";s:12:"EDIT_IN_LIST";s:1:"Y";s:13:"IS_SEARCHABLE";s:1:"N";s:8:"SETTINGS";s:116:"a:5:{s:4:"LEAD";s:1:"Y";s:7:"CONTACT";s:1:"Y";s:7:"COMPANY";s:1:"Y";s:4:"DEAL";s:1:"Y";s:12:"DYNAMIC_1046";s:1:"N";}";s:15:"EDIT_FORM_LABEL";s:9:"CRM Items";s:17:"LIST_COLUMN_LABEL";s:9:"CRM Items";s:17:"LIST_FILTER_LABEL";s:9:"CRM Items";s:13:"ERROR_MESSAGE";N;s:12:"HELP_MESSAGE";N;}i:1;a:18:{s:2:"ID";s:2:"15";s:9:"ENTITY_ID";s:14:"CALENDAR_EVENT";s:10:"FIELD_NAME";s:19:"UF_WEBDAV_CAL_EVENT";s:12:"USER_TYPE_ID";s:9:"disk_file";s:6:"XML_ID";s:19:"UF_WEBDAV_CAL_EVENT";s:4:"SORT";s:3:"100";s:8:"MULTIPLE";s:1:"Y";s:9:"MANDATORY";s:1:"N";s:11:"SHOW_FILTER";s:1:"N";s:12:"SHOW_IN_LIST";s:1:"N";s:12:"EDIT_IN_LIST";s:1:"Y";s:13:"IS_SEARCHABLE";s:1:"Y";s:8:"SETTINGS";s:6:"a:0:{}";s:15:"EDIT_FORM_LABEL";N;s:17:"LIST_COLUMN_LABEL";N;s:17:"LIST_FILTER_LABEL";N;s:13:"ERROR_MESSAGE";N;s:12:"HELP_MESSAGE";N;}}}';
return true;
?>