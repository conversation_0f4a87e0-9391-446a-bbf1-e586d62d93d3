<?
if($INCLUDE_FROM_CACHE!='Y')return false;
$datecreate = '001749062218';
$dateexpire = '001751654218';
$ser_content = 'a:2:{s:7:"CONTENT";s:0:"";s:4:"VARS";a:4:{s:4:"menu";a:5:{i:0;a:5:{s:5:"title";s:5:"Tools";s:6:"hidden";b:0;s:5:"_code";s:8:"favorite";s:4:"sort";i:100;s:5:"items";a:2:{i:0;a:6:{s:8:"imageUrl";s:78:"/bitrix/mobileapp/mobile/components/bitrix/more//images/favorite/icon-disk.png";s:5:"color";s:7:"#3CD162";s:5:"title";s:12:"Shared Drive";s:9:"imageName";s:9:"folder_24";s:5:"attrs";a:2:{s:7:"onclick";s:311:"
		ComponentHelper.openList({
			name:"user.disk",
			object:"list",
			version:"1_1d4820472c1d72749e6a07092378b0e2",
			componentParams:{userId: env.userId, ownerId: "shared_files_"+env.siteId, entityType:"common"},
			widgetParams:{titleParams: { text:"Shared Drive", type: "section"}, useSearch: true}
		});
";s:2:"id";s:10:"doc_shared";}s:6:"hidden";b:1;}i:1;a:6:{s:5:"title";s:12:"Shared Drive";s:8:"imageUrl";s:78:"/bitrix/mobileapp/mobile/components/bitrix/more//images/favorite/icon-disk.png";s:5:"color";s:7:"#b9bdc3";s:9:"imageName";s:9:"folder_24";s:5:"attrs";a:2:{s:7:"onclick";s:209:"
		PageManager.openList(
		{
			url:"/mobile/?mobile_action=disk_folder_list&type=common&path=/&entityId=shared_files_"+env.siteId,
			table_settings:
			{
				useTagsInSearch:"NO",
				type:"files"
			}
		});";s:2:"id";s:10:"doc_shared";}s:6:"hidden";b:1;}}}i:1;a:4:{s:5:"title";s:3:"CRM";s:4:"sort";i:120;s:6:"hidden";b:0;s:5:"items";a:1:{i:0;a:6:{s:5:"title";s:13:"My Activities";s:8:"imageUrl";s:80:"/bitrix/mobileapp/mobile/components/bitrix/more//images/crm/icon-crm-mydeals.png";s:9:"imageName";s:8:"my_deals";s:5:"color";s:7:"#8590a2";s:6:"hidden";b:0;s:5:"attrs";a:2:{s:3:"url";s:29:"/mobile/crm/activity/list.php";s:2:"id";s:17:"crm_activity_list";}}}}i:2;a:5:{s:5:"title";s:16:"Payment terminal";s:4:"sort";i:127;s:4:"code";s:8:"terminal";s:6:"hidden";b:0;s:5:"items";a:0:{}}i:3;a:4:{s:5:"title";s:10:"Workgroups";s:4:"sort";i:130;s:6:"hidden";b:0;s:5:"items";a:1:{i:0;a:5:{s:5:"title";s:8:"Intranet";s:9:"imageName";s:8:"intranet";s:8:"imageUrl";s:77:"/bitrix/mobileapp/mobile/components/bitrix/more//images/favorite/intranet.png";s:5:"color";s:7:"#0075FF";s:5:"attrs";a:1:{s:7:"onclick";s:591:"				ComponentHelper.openList({
					name: \'workgroups\',
					object: \'list\',
					version: "1.0.0.0.2_9684bdb5f384478b004f67b0e7356a05",
					componentParams: {
						siteId: "s1",
						siteDir: "/",
						pathTemplate: "/mobile/log/?group_id=#group_id#",
						calendarWebPathTemplate: "/workgroups/group/#group_id#/calendar/",
						features: "tasks,blog,files,calendar",
						mandatoryFeatures: "blog",
						currentUserId: "20"
					},
					widgetParams: {
						titleParams: {text: "Intranet", type: "section"},
						useSearch: false,
						doNotHideSearchResult: true
					}
				});";}}}}i:4;a:4:{s:5:"title";s:13:"Your Bitrix24";s:4:"sort";i:1;s:6:"hidden";b:0;s:5:"items";a:2:{i:0;a:6:{s:2:"id";s:5:"users";s:5:"title";s:9:"Employees";s:8:"imageUrl";s:79:"/bitrix/mobileapp/mobile/components/bitrix/more//images/favorite/icon-users.png";s:5:"color";s:7:"#AF22AF";s:9:"imageName";s:13:"three_persons";s:5:"attrs";a:2:{s:7:"counter";s:16:"total_invitation";s:7:"onclick";s:694:"			let inviteParams = {};
			try
			{
				inviteParams = JSON.parse(Application.sharedStorage(\'menuComponentSettings\').get("invite"));
			}
			catch (e)
			{
				//do nothing
			}
			PageManager.openComponent(\'JSStackComponent\', {
				scriptPath: "/mobileapp/jn/intranet%3Auser.list/?version=1_4ab43fe8c93ccd67b7e59813ac13e8c8",
				componentCode: "intranet.user.list",
				params: {
					canInvite: (inviteParams.canInviteUsers ? inviteParams.canInviteUsers : false),
					canUseTelephony: "N",
				},
				rootWidget: {
					name: \'layout\',
					componentCode: \'users\',
					settings: {
						objectName: "layout",
						titleParams: {text: "Employees", type: "section"},
					},
				},
			});";}}i:1;a:6:{s:2:"id";s:11:"tab_presets";s:5:"title";s:11:"Bottom menu";s:9:"imageName";s:11:"bottom_menu";s:5:"color";s:7:"#1E8EC2";s:8:"imageUrl";s:80:"/bitrix/mobileapp/mobile/components/bitrix/more//images/favorite/bottom_menu.png";s:5:"attrs";a:4:{s:15:"showHighlighted";b:1;s:20:"highlightWithCounter";b:1;s:7:"counter";s:16:"menu_tab_presets";s:7:"onclick";s:253:"	PageManager.openComponent(\'JSStackComponent\',{
		scriptPath: availableComponents[\'tab.presets\'].publicUrl,
		rootWidget:{
		name: \'layout\',
		settings:{
			objectName: \'layout\',
			titleParams: { text: "Bottom menu", useLargeTitleMode: true}
		}
	}
});";}}}}}s:14:"popupMenuItems";a:1:{i:0;a:6:{s:5:"title";s:14:"Switch account";s:11:"sectionCode";s:4:"menu";s:2:"id";s:14:"switch_account";s:8:"iconName";s:7:"log_out";s:7:"iconUrl";N;s:7:"onclick";s:23:"				Application.exit();";}}s:4:"host";s:21:"crm.reizedispatch.com";s:4:"user";a:2:{s:8:"fullName";s:14:"Nare Mkrtchyan";s:6:"avatar";s:0:"";}}}';
return true;
?>