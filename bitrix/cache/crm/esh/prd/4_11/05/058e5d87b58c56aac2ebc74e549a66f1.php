<?
if($INCLUDE_FROM_CACHE!='Y')return false;
$datecreate = '001749065609';
$dateexpire = '001749152009';
$ser_content = 'a:2:{s:7:"CONTENT";s:0:"";s:4:"VARS";a:1:{i:0;a:8:{s:8:"presetId";i:1;s:15:"presetCountryId";i:122;s:11:"requisiteId";i:4;s:12:"entityTypeId";i:4;s:8:"entityId";i:11;s:13:"requisiteData";s:3067:"{"fields":{"ID":"4","ENTITY_TYPE_ID":"4","ENTITY_ID":"11","PRESET_ID":"1","DATE_CREATE":"05\\/13\\/2025 06:10:51 pm","DATE_MODIFY":null,"CREATED_BY_ID":"1","MODIFY_BY_ID":null,"NAME":"Company","CODE":null,"XML_ID":null,"ORIGINATOR_ID":null,"ACTIVE":"Y","ADDRESS_ONLY":"Y","SORT":"500","RQ_NAME":null,"RQ_FIRST_NAME":null,"RQ_LAST_NAME":null,"RQ_SECOND_NAME":null,"RQ_COMPANY_ID":null,"RQ_COMPANY_NAME":null,"RQ_COMPANY_FULL_NAME":null,"RQ_COMPANY_REG_DATE":null,"RQ_DIRECTOR":null,"RQ_ACCOUNTANT":null,"RQ_CEO_NAME":null,"RQ_CEO_WORK_POS":null,"RQ_CONTACT":null,"RQ_EMAIL":null,"RQ_PHONE":null,"RQ_FAX":null,"RQ_IDENT_TYPE":null,"RQ_IDENT_DOC":null,"RQ_IDENT_DOC_SER":null,"RQ_IDENT_DOC_NUM":null,"RQ_IDENT_DOC_PERS_NUM":null,"RQ_IDENT_DOC_DATE":null,"RQ_IDENT_DOC_ISSUED_BY":null,"RQ_IDENT_DOC_DEP_CODE":null,"RQ_INN":null,"RQ_KPP":null,"RQ_USRLE":null,"RQ_IFNS":null,"RQ_OGRN":null,"RQ_OGRNIP":null,"RQ_OKPO":null,"RQ_OKTMO":null,"RQ_OKVED":null,"RQ_EDRPOU":null,"RQ_DRFO":null,"RQ_KBE":null,"RQ_IIN":null,"RQ_BIN":null,"RQ_ST_CERT_SER":null,"RQ_ST_CERT_NUM":null,"RQ_ST_CERT_DATE":null,"RQ_VAT_PAYER":"N","RQ_VAT_ID":null,"RQ_VAT_CERT_SER":null,"RQ_VAT_CERT_NUM":null,"RQ_VAT_CERT_DATE":null,"RQ_RESIDENCE_COUNTRY":null,"RQ_BASE_DOC":null,"RQ_REGON":null,"RQ_KRS":null,"RQ_PESEL":null,"RQ_LEGAL_FORM":null,"RQ_SIRET":null,"RQ_SIREN":null,"RQ_CAPITAL":null,"RQ_RCS":null,"RQ_CNPJ":null,"RQ_STATE_REG":null,"RQ_MNPL_REG":null,"RQ_CPF":null,"RQ_SIGNATURE":null,"RQ_STAMP":null,"RQ_ADDR":{"6":"{\\u0022id\\u0022:12,\\u0022latitude\\u0022:\\u002239.818113\\u0022,\\u0022longitude\\u0022:\\u0022-86.077402\\u0022,\\u0022languageId\\u0022:\\u0022en\\u0022,\\u0022fieldCollection\\u0022:{\\u002250\\u0022:\\u002246218\\u0022,\\u0022100\\u0022:\\u0022United States of America\\u0022,\\u0022200\\u0022:\\u0022Indiana\\u0022,\\u0022210\\u0022:\\u0022Marion County\\u0022,\\u0022300\\u0022:\\u0022Indianapolis\\u0022,\\u0022340\\u0022:\\u0022East 34th Street\\u0022,\\u0022400\\u0022:\\u00225403\\u0022,\\u0022410\\u0022:\\u0022East 34th Street, 5403\\u0022},\\u0022links\\u0022:[{\\u0022entityId\\u0022:\\u00226.8.4\\u0022,\\u0022entityType\\u0022:\\u0022CRM_REQUISITE_ADDRESS\\u0022}],\\u0022location\\u0022:{\\u0022id\\u0022:3,\\u0022code\\u0022:\\u0022a3cb8054-f0f8-4815-915d-b8ee3ea2289c\\u0022,\\u0022externalId\\u0022:\\u0022W1030570874\\u0022,\\u0022sourceCode\\u0022:\\u0022OSM\\u0022,\\u0022type\\u0022:400,\\u0022name\\u0022:\\u0022East 34th Street, 5403\\u0022,\\u0022languageId\\u0022:\\u0022en\\u0022,\\u0022latitude\\u0022:\\u002239.818113\\u0022,\\u0022longitude\\u0022:\\u0022-86.077402\\u0022,\\u0022fieldCollection\\u0022:[],\\u0022address\\u0022:{\\u0022id\\u0022:0,\\u0022latitude\\u0022:\\u002239.818113\\u0022,\\u0022longitude\\u0022:\\u0022-86.077402\\u0022,\\u0022languageId\\u0022:\\u0022en\\u0022,\\u0022fieldCollection\\u0022:{\\u0022400\\u0022:\\u0022East 34th Street, 5403\\u0022},\\u0022links\\u0022:[]}}}"}},"viewData":{"title":"Company","fields":[],"subtitle":""},"bankDetailFieldsList":[],"bankDetailViewDataList":[],"bankDetailIdSelected":0,"formattedAddresses":{"6":"East 34th Street, 5403, Indianapolis Marion County Indiana 46218, UNITED STATES OF AMERICA"}}";s:17:"requisiteDataSign";s:64:"97daa18b318b09d6466f79d87d6841a28180e3c1ff579cdd8f1d5162064f93a4";s:8:"selected";b:1;}}}';
return true;
?>