#!/bin/bash
# Pre-update backup script for Bitrix24 customizations

echo "Creating backup of Bitrix24 customizations..."

# Create backup directory with timestamp
BACKUP_DIR="/tmp/bitrix24_customizations_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup the modified template header
echo "Backing up template header..."
cp -p /var/www/html/bx-site/bitrix/templates/bitrix24/header.php "$BACKUP_DIR/header.php.backup"

# Backup entire local directory
echo "Backing up local directory..."
cp -rp /var/www/html/bx-site/local "$BACKUP_DIR/"

# Create database backup
echo "Creating database backup..."
mysqldump --single-transaction --routines --triggers bitrix24_db > "$BACKUP_DIR/database_backup.sql"

# Document current version
echo "Documenting current version..."
cat /var/www/html/bx-site/bitrix/modules/main/classes/general/version.php > "$BAC<PERSON>UP_DIR/current_version.txt"

# Create restoration script
cat > "$BACKUP_DIR/restore_customizations.sh" << 'EOF'
#!/bin/bash
# Restoration script for Bitrix24 customizations

BACKUP_DIR=$(dirname "$0")
SITE_DIR="/var/www/html/bx-site"

echo "Restoring customizations from backup..."

# Restore local directory (should not be needed, but just in case)
if [ ! -d "$SITE_DIR/local/php_interface/include" ]; then
    echo "Restoring local directory..."
    cp -rp "$BACKUP_DIR/local" "$SITE_DIR/"
fi

# Check if header.php was overwritten and restore role-based menu system
if ! grep -q "Role 8\|Role 9" "$SITE_DIR/bitrix/templates/bitrix24/header.php"; then
    echo "Header.php was overwritten. Manual intervention required."
    echo "Please check the role-based menu customizations."
    echo "Backup file: $BACKUP_DIR/header.php.backup"
fi

echo "Restoration complete. Please test all customizations."
EOF

chmod +x "$BACKUP_DIR/restore_customizations.sh"

echo "Backup completed in: $BACKUP_DIR"
echo "Run the update, then execute: $BACKUP_DIR/restore_customizations.sh"
