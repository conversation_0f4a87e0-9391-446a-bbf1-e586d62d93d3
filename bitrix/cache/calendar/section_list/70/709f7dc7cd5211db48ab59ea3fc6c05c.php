<?
if($INCLUDE_FROM_CACHE!='Y')return false;
$datecreate = '001748974348';
$dateexpire = '001751566348';
$ser_content = 'a:2:{s:7:"CONTENT";s:0:"";s:4:"VARS";a:3:{s:8:"arResult";a:1:{i:0;a:28:{s:2:"ID";s:2:"10";s:4:"NAME";s:15:"<PERSON><PERSON>pet<PERSON>";s:6:"XML_ID";s:32:"841ec6c712e981a7ef8769b54840f3e5";s:11:"EXTERNAL_ID";N;s:16:"GAPI_CALENDAR_ID";N;s:6:"ACTIVE";s:1:"Y";s:11:"DESCRIPTION";s:39:"The calendar was created by the system.";s:5:"COLOR";s:7:"#9DCF00";s:10:"TEXT_COLOR";N;s:6:"EXPORT";a:2:{s:5:"ALLOW";b:1;s:4:"LINK";s:126:"&type=user&owner=19&ncc=1&user=19&sec_id=10&sign=6682ea48fe034d193c41a36193531a3a&bx_hit_hash=XTPWgEVPVXtiax4iTrOl0501KYFVUR9e";}s:4:"SORT";s:3:"100";s:8:"CAL_TYPE";s:4:"user";s:8:"OWNER_ID";s:2:"19";s:10:"CREATED_BY";s:2:"19";s:9:"PARENT_ID";N;s:11:"DATE_CREATE";s:22:"06/02/2025 09:37:00 am";s:11:"TIMESTAMP_X";s:22:"06/03/2025 10:14:04 am";s:12:"DAV_EXCH_CAL";N;s:12:"DAV_EXCH_MOD";N;s:11:"CAL_DAV_CON";N;s:11:"CAL_DAV_CAL";N;s:11:"CAL_DAV_MOD";N;s:11:"IS_EXCHANGE";b:0;s:10:"SYNC_TOKEN";N;s:10:"PAGE_TOKEN";N;s:13:"EXTERNAL_TYPE";s:5:"local";s:10:"OUTLOOK_JS";s:10:"needAction";s:9:"IS_COLLAB";b:0;}}s:12:"arSectionIds";a:1:{i:0;i:10;}s:11:"permissions";a:0:{}}}';
return true;
?>